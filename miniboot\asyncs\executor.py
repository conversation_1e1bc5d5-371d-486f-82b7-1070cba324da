#!/usr/bin/env python
"""
* @author: cz
* @description: 异步执行器实现

参考 Spring Boot TaskExecutor 设计，提供简洁的异步执行抽象，支持任务队列和指标监控。
"""

import asyncio
import queue
import threading
import time
from concurrent.futures import Future, ThreadPoolExecutor
from dataclasses import dataclass, field
from typing import Any, Callable, Optional

from .base import Executor
from .properties import ThreadPoolConfig


@dataclass
class ExecutorMetrics:
    """执行器指标"""
    executor_name: str
    # 任务统计
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    running_tasks: int = 0
    # 队列统计
    queue_size: int = 0
    queue_capacity: int = 0
    # 线程池统计
    active_threads: int = 0
    pool_size: int = 0
    max_pool_size: int = 0
    # 时间统计
    start_time: float = field(default_factory=time.time)
    last_activity_time: float = field(default_factory=time.time)

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        uptime = time.time() - self.start_time
        success_rate = (self.completed_tasks / self.total_tasks * 100) if self.total_tasks > 0 else 0.0

        return {
            "executor_name": self.executor_name,
            # 任务指标
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "running_tasks": self.running_tasks,
            "success_rate": round(success_rate, 2),
            # 队列指标
            "queue_size": self.queue_size,
            "queue_capacity": self.queue_capacity,
            "queue_usage": round((self.queue_size / self.queue_capacity * 100) if self.queue_capacity > 0 else 0, 2),
            # 线程池指标
            "active_threads": self.active_threads,
            "pool_size": self.pool_size,
            "max_pool_size": self.max_pool_size,
            "pool_usage": round((self.pool_size / self.max_pool_size * 100) if self.max_pool_size > 0 else 0, 2),
            # 时间指标
            "uptime_seconds": round(uptime, 2),
            "last_activity_time": self.last_activity_time,
        }



class ThreadPoolTaskExecutor(Executor):
    """线程池任务执行器

    参考 Spring Boot 的 ThreadPoolTaskExecutor 实现，在线程池中执行同步任务。

    支持功能：
    - 任务队列管理
    - 拒绝策略
    - 指标监控
    - 同步和异步函数执行
    """

    def __init__(self, config: ThreadPoolConfig, name: str = "default"):
        """初始化线程池任务执行器

        Args:
            config: 线程池配置
            name: 执行器名称
        """
        self.config = config
        self.name = name
        self._executor: Optional[ThreadPoolExecutor] = None
        self._lock = threading.RLock()
        self._shutdown = False

        # 任务队列（如果配置了队列容量）
        if config.queue_capacity > 0:
            self._task_queue: Optional[queue.Queue] = queue.Queue(maxsize=config.queue_capacity)
        else:
            self._task_queue = None

        # 指标收集
        self._metrics = ExecutorMetrics(
            executor_name=name,
            queue_capacity=config.queue_capacity,
            max_pool_size=config.max_size
        )

        # 运行中的任务跟踪
        self._running_tasks: set[str] = set()
        self._task_counter = 0

        # 延迟初始化
        self._initialize()

    def _initialize(self):
        """初始化线程池"""
        if self._executor is None:
            with self._lock:
                if self._executor is None:
                    self._executor = ThreadPoolExecutor(
                        max_workers=self.config.max_size,
                        thread_name_prefix=self.config.thread_name_prefix
                    )
                    self._metrics.pool_size = self.config.max_size

    def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:
        """提交任务到线程池（支持同步和异步函数）

        Args:
            func: 要执行的函数（同步或异步）
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            Future: 任务执行结果

        Raises:
            RuntimeError: 当执行器已关闭或队列已满时
        """
        if self._shutdown:
            raise RuntimeError(f"Executor '{self.name}' has been shut down")

        with self._lock:
            self._task_counter += 1
            task_id = f"{self.name}-task-{self._task_counter}"

            # 更新指标
            self._metrics.total_tasks += 1
            self._metrics.last_activity_time = time.time()

        # 检查是否为异步函数
        if asyncio.iscoroutinefunction(func):
            # 异步函数：包装为同步执行
            def async_wrapper():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(func(*args, **kwargs))
                finally:
                    loop.close()

            actual_func = async_wrapper
            actual_args = ()
            actual_kwargs = {}
        else:
            # 同步函数：直接使用
            actual_func = func
            actual_args = args
            actual_kwargs = kwargs

        if self._task_queue is not None:
            # 有队列限制的情况
            try:
                # 尝试放入队列（非阻塞）
                self._task_queue.put_nowait((task_id, actual_func, actual_args, actual_kwargs))
                return self._executor.submit(self._execute_from_queue)
            except queue.Full:
                # 队列满，根据拒绝策略处理
                return self._handle_rejection(task_id, actual_func, *actual_args, **actual_kwargs)
        else:
            # 无队列限制，直接提交
            return self._executor.submit(self._execute_task, task_id, actual_func, actual_args, actual_kwargs)

    def _execute_from_queue(self) -> Any:
        """从队列中取任务执行"""
        try:
            task_id, func, args, kwargs = self._task_queue.get(timeout=1.0)
            return self._execute_task(task_id, func, args, kwargs)
        except queue.Empty:
            return None
        # 注意：不调用 task_done()，因为我们没有使用 join()

    def _execute_task(self, task_id: str, func: Callable, args: tuple, kwargs: dict) -> Any:
        """执行单个任务"""
        try:
            # 标记任务开始
            with self._lock:
                self._running_tasks.add(task_id)
                self._metrics.running_tasks = len(self._running_tasks)

            # 执行任务
            result = func(*args, **kwargs)

            # 标记任务完成
            with self._lock:
                self._metrics.completed_tasks += 1

            return result

        except Exception:
            # 标记任务失败
            with self._lock:
                self._metrics.failed_tasks += 1
            raise
        finally:
            # 清理任务
            with self._lock:
                self._running_tasks.discard(task_id)
                self._metrics.running_tasks = len(self._running_tasks)

    def _handle_rejection(self, task_id: str, func: Callable, *args, **kwargs) -> Any:
        """处理任务拒绝

        Args:
            task_id: 任务ID
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            Any: 任务执行结果

        Raises:
            RuntimeError: 当拒绝策略为 abort 时
        """
        policy = getattr(self.config, 'rejection_policy', 'abort')

        if policy == "abort":
            raise RuntimeError(f"Task rejected by executor '{self.name}': queue is full")
        elif policy == "caller_runs":
            # 在调用线程中执行
            return self._execute_task(task_id, func, args, kwargs)
        elif policy == "discard":
            # 丢弃任务
            return None
        elif policy == "discard_oldest":
            # 丢弃最老的任务，执行新任务
            try:
                if self._task_queue:
                    self._task_queue.get_nowait()  # 丢弃一个任务
                    self._task_queue.put_nowait((task_id, func, args, kwargs))
                    return self._executor.submit(self._execute_from_queue)
            except (queue.Empty, queue.Full):
                pass
            return None
        else:
            raise ValueError(f"Unknown rejection policy: {policy}")

    def get_metrics(self) -> dict[str, Any]:
        """获取执行器指标

        Returns:
            dict: 包含执行器各项指标的字典
        """
        with self._lock:
            # 更新实时指标
            if self._task_queue:
                self._metrics.queue_size = self._task_queue.qsize()

            if self._executor:
                # 获取线程池状态
                self._metrics.active_threads = getattr(self._executor, '_threads', 0)

            return self._metrics.to_dict()

    def shutdown(self, wait: bool = True) -> None:
        """关闭线程池

        Args:
            wait: 是否等待任务完成
        """
        # 先设置关闭标志，但不持有锁太久
        executor_to_shutdown = None
        with self._lock:
            if not self._shutdown:
                self._shutdown = True
                executor_to_shutdown = self._executor

        # 在锁外调用shutdown，避免死锁
        if executor_to_shutdown:
            executor_to_shutdown.shutdown(wait=wait)

    def is_shutdown(self) -> bool:
        """检查是否已关闭

        Returns:
            bool: 是否已关闭
        """
        return self._shutdown

    def get_active_count(self) -> int:
        """获取活跃线程数

        Returns:
            int: 当前活跃的线程数
        """
        if self._executor and not self._shutdown:
            return getattr(self._executor, '_threads', 0)
        return 0

    def get_pool_size(self) -> int:
        """获取线程池大小

        Returns:
            int: 线程池最大大小
        """
        return self.config.max_size

    def get_queue_size(self) -> int:
        """获取当前队列大小

        Returns:
            int: 队列中等待的任务数
        """
        if self._task_queue:
            return self._task_queue.qsize()
        return 0

    def get_config(self) -> ThreadPoolConfig:
        """获取线程池配置

        Returns:
            ThreadPoolConfig: 线程池配置
        """
        return self.config

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()
