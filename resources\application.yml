# Mini-Boot 应用主配置文件

miniboot:
  # Profile 配置
  profiles:
    active: dev # 默认激活 dev profile

  # 应用基本信息
  application:
    name: mini-boot-app
    version: 1.0.0

  # 自动配置设置
  autoconfigure:
    enabled: true # 是否启用自动配置，默认为true

    # 排除特定的自动配置
    exclude: []
    # 示例：
    # exclude:
    #   - "unwanted-auto-configuration"
    #   - "another-excluded-config"

    # 额外扫描的包（用于发现自定义自动配置）
    packages: []
    # 示例：
    # packages:
    #   - "com.example.custom.autoconfigure"
    #   - "org.mycompany.starters"

  # Banner 横幅配置
  banner:
    enabled: true # 是否启用启动横幅
    mode: console # 输出模式：console(控制台), log(日志), off(关闭)
    location: classpath:banner.txt # 横幅文件位置，支持 classpath: 和 file: 前缀
    charset: UTF-8 # 横幅文件字符编码

    # 显示选项
    show-version: true # 是否显示应用版本信息
    show-environment: true # 是否显示环境信息
    show-startup-time: true # 是否显示启动时间

    # 样式选项
    colors: true # 是否启用彩色输出（仅控制台模式）
    width: 80 # 横幅最大宽度

  # 服务器配置
  server:
    port: 8080
    host: 0.0.0.0
    name: ${miniboot.application.name}
    full-name: ${miniboot.application.name}-${miniboot.application.version}
    url: http://${miniboot.server.host}:${miniboot.server.port}

  # 数据库配置
  datasource:
    url: sqlite:///data/app.db
    driver: sqlite3
    database-name: ${miniboot.application.name:miniboot}-db
    connection-string: ${miniboot.datasource.driver}://${miniboot.datasource.database-name}

  # 日志配置
  logging:
    level: INFO # 全局日志级别：TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL

    # 控制台输出配置
    console:
      enabled: true # 是否启用控制台输出
      level: INFO # 控制台日志级别
      colorize: true # 是否启用彩色输出
      format: "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <blue>Thread-{thread.id}</blue> | <cyan>{name}</cyan>:<yellow>{function}</yellow>:<white>{line}</white> - <level>{message}</level>"

    # 文件输出配置
    file:
      enabled: true # 是否启用文件输出
      path: logs/app.log # 日志文件路径
      level: DEBUG # 文件日志级别
      rotation: "500 MB" # 日志轮转大小
      retention: "10 days" # 日志保留时间
      compression: "zip" # 压缩格式：zip, gz, bz2, xz
      format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | Thread-{thread.id} | {name}:{function}:{line} - {message}"

    # 格式配置
    format:
      pattern: null # 自定义格式模式，如果设置则覆盖上面的format
      color: true # 是否启用颜色（控制台）

    # 轮转配置
    rotation:
      size: "500 MB" # 按大小轮转
      time: "10 days" # 按时间保留

  # Web 配置
  web:
    # 基础配置
    enabled: true # 是否启用Web功能
    host: 0.0.0.0 # 服务器主机地址
    port: 8081 # 服务器端口
    title: "Mini-Boot Application" # 应用标题
    description: "Mini-Boot Web Application" # 应用描述
    version: ${miniboot.application.version} # 应用版本
    log-level: INFO # Web服务器日志级别

    # CORS 跨域配置
    cors:
      enabled: true # 是否启用CORS
      allowed-origins: ["*"] # 允许的源
      allowed-methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"] # 允许的HTTP方法
      allowed-headers: ["*"] # 允许的请求头
      allow-credentials: true # 是否允许携带凭证

    # 压缩配置
    compression:
      enabled: true # 是否启用响应压缩
      min-size: 1024 # 最小压缩大小(字节)

    # 日志配置
    logging:
      enabled: true # 是否启用请求日志
      log-requests: true # 是否记录请求
      log-responses: true # 是否记录响应
      log-headers: true # 是否包含请求头
      log-body: true # 是否包含请求体
      log-query-params: true # 是否记录查询参数
      max-body-size: 10240 # 最大请求体大小(字节)

    # API文档配置
    docs:
      enabled: true # 是否启用API文档
      title: "Mini-Boot API" # 文档标题
      description: "Mini-Boot Web API Documentation" # 文档描述

    # 静态文件配置
    static:
      enabled: true # 是否启用静态文件服务，默认禁用以减少资源消耗
      directory: "static" # 静态文件目录
      mount-path: "/static" # 挂载路径
      html: true # 是否启用HTML文件服务
      check-dir: true # 是否检查目录存在

    # 注意：背压控制、任务调度等高级功能现在由独立的 Starters 提供
    # 请参考以下配置节：
    # - miniboot.starters.web.backpressure - 背压控制配置
    # - miniboot.starters.web.task-scheduler - 任务调度配置
    # - miniboot.starters.web.load-monitoring - 负载监控配置

  # 调度器配置
  scheduler:
    # 基础配置
    enabled: true # 是否启用调度功能
    auto-startup: true # 是否自动启动调度器
    timezone: Asia/Shanghai # 时区设置，默认为系统时区
    daemon: true # 是否以守护进程模式运行

    # 并发配置
    concurrency:
      max-workers: 10 # 最大工作线程数
      max-instances: 3 # 单个任务最大并发实例数
      coalesce: false # 是否合并错过的执行
      coalesce-policy: latest # 合并策略：latest(最新), earliest(最早), all(全部)
      misfire-grace-time: 30 # 错过执行宽限时间(秒)
      misfire-grace-policy: immediate # 错过执行策略：none(不执行), immediate(立即执行), delayed(延迟执行)

    # 作业存储配置
    job-store:
      type: memory # 存储类型：memory(内存), database(数据库)

      # 数据库配置（当 type=database 时生效）
      database:
        url: ${miniboot.datasource.connection-string}/scheduler # 数据库连接URL
        table-prefix: scheduler_ # 表前缀
        metadata:
          create-tables: true # 是否自动创建表
          drop-tables: false # 是否在启动时删除现有表
        connection-pool:
          pool-size: 5 # 连接池大小
          max-overflow: 10 # 最大溢出连接数
          pool-timeout: 30 # 连接超时时间(秒)
          pool-recycle: 3600 # 连接回收时间(秒)

    # 执行器配置
    executors:
      # 默认线程池执行器
      default:
        type: threadpool # 执行器类型：threadpool(线程池), processpool(进程池), asyncio(异步IO)
        max-workers: ${miniboot.scheduler.concurrency.max-workers}
        thread-name-prefix: scheduler-

      # 异步IO执行器
      asyncio:
        type: asyncio
        max-workers: 10

      # 高并发执行器
      high-concurrency:
        type: threadpool
        max-workers: 50
        thread-name-prefix: high-concurrency-

      # CPU密集型执行器
      cpu-intensive:
        type: processpool
        max-workers: 4 # 通常设置为CPU核心数

    # 任务默认配置
    job-defaults:
      coalesce: ${miniboot.scheduler.concurrency.coalesce}
      max-instances: ${miniboot.scheduler.concurrency.max-instances}
      misfire-grace-time: ${miniboot.scheduler.concurrency.misfire-grace-time}
      replace-existing: false # 是否替换已存在的任务

    # 触发器配置
    trigger:
      timezone: ${miniboot.scheduler.timezone} # 触发器时区
      start-date: null # 开始日期 (ISO格式: 2024-01-01T00:00:00)
      end-date: null # 结束日期 (ISO格式: 2024-12-31T23:59:59)
      jitter: null # 随机延迟(秒)，用于避免任务同时执行

    # 监控配置
    monitoring:
      enabled: true # 是否启用监控
      metrics:
        enabled: true # 是否启用指标收集
        export-interval: 60 # 指标导出间隔(秒)
        include-task-details: true # 是否包含任务详细信息

      health-check:
        enabled: true # 是否启用健康检查
        interval: 30 # 健康检查间隔(秒)
        timeout: 10 # 健康检查超时(秒)

      events:
        enabled: true # 是否启用事件发布
        publish-execution-events: true # 是否发布任务执行事件
        publish-lifecycle-events: true # 是否发布调度器生命周期事件

    # 任务管理配置
    task-management:
      enabled: true # 是否启用高级任务管理功能
      retry:
        enabled: true # 是否启用重试机制
        max-attempts: 3 # 最大重试次数
        delay: 1.0 # 重试延迟(秒)
        backoff-multiplier: 2.0 # 退避倍数
        max-delay: 60.0 # 最大重试延迟(秒)

      fixed-delay:
        enabled: true # 是否启用固定延迟任务特殊处理
        avoid-overlap: true # 是否避免任务重叠执行

    # 性能调优配置
    performance:
      thread-pool:
        core-pool-size: 5 # 核心线程池大小
        keep-alive-time: 60 # 线程保活时间(秒)
        queue-capacity: 100 # 任务队列容量

      gc:
        cleanup-interval: 300 # 垃圾回收间隔(秒)
        max-idle-time: 3600 # 最大空闲时间(秒)

    # 安全配置
    security:
      enabled: false # 是否启用安全功能
      authentication:
        required: false # 是否需要认证

    # 开发配置
    development:
      debug: false # 是否启用调试模式
      log-execution-details: false # 是否记录执行详情
      profile-performance: false # 是否启用性能分析

  # 异步处理配置
  async:
    # 基础配置
    enabled: true # 是否启用异步功能
    default-timeout: 30.0 # 默认超时时间(秒)

    # 线程池执行器配置
    executor:
      core-size: 4 # 核心线程数
      max-size: 8 # 最大线程数
      queue-capacity: 100 # 队列容量
      keep-alive: 60 # 线程保活时间(秒)
      thread-name-prefix: "miniboot-async-" # 线程名称前缀
      allow-core-thread-timeout: false # 是否允许核心线程超时
      rejection-policy: "caller_runs" # 拒绝策略：abort, caller_runs, discard, discard_oldest

  # Starters 配置
  starters:
    # Web Starter - Web 框架集成
    web:
      enabled: true # Web 模块总开关，影响 Actuator Web 集成

      # 背压控制 Starter
      backpressure:
        enabled: true # 是否启用背压控制
        circuit-breaker-enabled: true # 是否启用熔断器
        rate-limiting-enabled: true # 是否启用限流
        max-requests-per-second: 1000 # 最大每秒请求数
        cpu-threshold: 0.8 # CPU使用率阈值
        memory-threshold: 0.85 # 内存使用率阈值
        response-time-threshold: 2.0 # 响应时间阈值(秒)
        failure-threshold: 5 # 熔断器失败阈值
        recovery-timeout: 30.0 # 恢复超时(秒)
        degradation-enabled: true # 是否启用服务降级
        monitoring-enabled: true # 是否启用性能监控

      # 任务调度 Starter
      task-scheduler:
        enabled: true # 是否启用任务调度器
        intelligent-scheduling: true # 是否启用智能调度
        max-concurrent-tasks: 100 # 最大并发任务数量
        worker-threads: 4 # 工作线程数量
        task-timeout: 30.0 # 任务执行超时时间(秒)
        queue-capacity: 1000 # 任务队列容量
        performance-profiling: true # 是否启用性能分析
        adaptive-scheduling: true # 是否启用自适应调度
        monitoring-enabled: true # 是否启用监控

      # 负载监控 Starter
      load-monitoring:
        enabled: false # 是否启用负载监控（暂时禁用以避免阻塞）
        monitoring-interval: 3.0 # 监控间隔(秒)
        cpu-monitoring-enabled: true # 是否启用CPU监控
        memory-monitoring-enabled: true # 是否启用内存监控
        disk-monitoring-enabled: false # 是否启用磁盘监控
        network-monitoring-enabled: false # 是否启用网络监控
        alert-thresholds:
          cpu-warning: 0.7 # CPU警告阈值
          cpu-critical: 0.9 # CPU临界阈值
          memory-warning: 0.75 # 内存警告阈值
          memory-critical: 0.95 # 内存临界阈值

    # Actuator Starter - 监控和管理功能
    actuator:
      enabled: true # Actuator 总开关
      auto-start: true # 自动启动

      # 上下文配置
      context:
        enabled: true # 是否启用 ActuatorContext

      # 端点配置
      endpoints:
        enabled: true # 是否启用端点注册表

      # 健康检查配置
      health:
        enabled: true # 是否启用健康检查端点

      # 信息端点配置
      info:
        enabled: true # 是否启用信息端点

      # 指标采集配置
      metrics:
        enabled: true # 指标采集总开关
        collection-interval: 30s # 采集间隔
        cache-enabled: true # 缓存启用
        async-collection: true # 异步采集
        batch-size: 100 # 批处理大小

        # 核心模块指标采集配置（对应 autoconfigure 中的配置类）
        core-modules:
          bean: true # Bean 工厂指标采集
          scheduler: true # 异步调度指标采集
          context: true # 应用上下文指标采集
          env: true # 环境配置指标采集

      # 线程转储端点配置
      threaddump:
        enabled: true # 是否启用线程转储端点

      # 环境信息端点配置
      env:
        enabled: true # 是否启用环境信息端点
        show-sensitive: false # 是否显示敏感信息
        max-sensitivity: "internal" # 最大敏感级别 (public/internal/confidential/secret)

        # 自定义指标配置
        custom:
          enabled: true # 启用自定义指标
          auto-time-requests: true # 自动为 HTTP 请求计时
          auto-count-requests: true # 自动统计 HTTP 请求次数

          # 指标命名配置
          naming:
            prefix: "miniboot" # 指标名称前缀
            separator: "." # 分隔符

          # 标签配置
          common-tags: # 所有指标的公共标签
            application: ${miniboot.application.name}
            environment: "production"
            version: ${miniboot.application.version}

          # 导出配置
          export:
            prometheus:
              enabled: true # 启用 Prometheus 格式导出
              endpoint: "/actuator/prometheus"
            json:
              enabled: true # 启用 JSON 格式导出
              pretty-print: true # JSON 格式化输出

          # 性能配置
          performance:
            collection-interval: 10s # 指标收集间隔
            max-metrics: 10000 # 最大指标数量
            cleanup-interval: 300s # 清理过期指标间隔

        # 自定义收集器
        custom-collectors: []

      # Web 集成配置（条件化）
      web:
        enabled: true # Web 集成开关
        base-path: "/actuator" # 基础路径
        port: null # null 表示使用主应用端口
        cors-enabled: true # CORS 支持
        cors-origins: ["*"] # CORS 允许的源

        # 路由注册配置
        routes:
          enabled: true # 是否启用动态路由注册
          auto-register: true # 是否自动注册端点路由

        # 中间件配置
        middleware:
          enabled: false # 是否启用 Web 中间件（可选功能）
          cors: true # CORS 中间件
          logging: true # 请求日志中间件

        # 端点配置
        endpoints:
          health: true # 健康检查端点
          info: true # 应用信息端点
          metrics: true # 指标端点
          beans: true # Bean 信息端点
          env: true # 环境信息端点
          threaddump: true # 线程转储端点
          custom: [] # 自定义端点列表

        # 安全配置
        security-enabled: false
        allowed-ips: []

      # 安全配置
      security:
        enabled: false
        authentication-required: false
        api-key: null
        allowed-roles: []
        rate-limiting: true
        max-requests-per-minute: 100

      # 性能监控配置
      performance:
        collection-interval: 2.0 # 性能数据收集间隔（秒）
        cache-ttl: 30.0 # 缓存生存时间（秒）

      # 全局配置
      graceful-shutdown: true
      shutdown-timeout: 30
      debug: false
      performance-monitoring: true

    # Mock Starter - 测试环境Mock功能
    mock:
      enabled: false # 是否启用Mock功能
      auto_mock: true # 是否自动Mock外部依赖
      data_source: "random" # 数据源类型：random, file, database
      prefix: "[MOCK]" # Mock日志前缀

    # Database Starter - 数据库操作功能
    database:
      enabled: false # 是否启用数据库功能
      auto_connect: true # 是否自动连接数据库
      url: "sqlite:///app.db" # 数据库连接URL
      driver: "sqlite" # 数据库驱动类型
      pool_size: 5 # 连接池大小

    # Monitor Starter - 监控功能
    monitor:
      enabled: false # 是否启用监控功能
      auto_start: true # 是否自动启动监控
      collect_interval: 60 # 数据收集间隔（秒）
      metrics_enabled: true # 是否启用指标收集
      health_enabled: true # 是否启用健康检查

    # WebSocket Starter - WebSocket实时通信功能
    websocket:
      # 基础配置
      enabled: false # 是否启用WebSocket功能，现在启用进行测试
      path: "/ws" # WebSocket端点路径
      max-message-size: 524288 # 最大消息大小(512KB)

      # 服务器配置
      server:
        host: 0.0.0.0 # 服务器主机地址
        port: 8080 # 服务器端口
        read-timeout: 10 # 读取超时时间(秒)
        write-timeout: 10 # 写入超时时间(秒)
        idle-timeout: 60 # 空闲超时时间(秒)

      # 安全配置
      security:
        allowed-origins: ["*"] # 允许的源地址
        auth:
          enabled: false # 是否启用认证
          token-header: "Authorization" # Token请求头
          token-query-param: "token" # Token查询参数
          token-cookie: "auth_token" # Token Cookie名称
        jwt:
          enabled: false # 是否启用JWT认证
          secret-key: "mini-boot-websocket-jwt-secret-key" # JWT密钥
          issuer: "mini-boot-websocket" # JWT签发者
          expiration-time: 3600 # JWT过期时间(秒)

      # 压缩配置
      compression:
        enabled: true # 是否启用消息压缩
        level: 6 # 压缩级别(1-9)
        algorithm: "gzip" # 压缩算法(gzip/deflate/lzma)
        min-size: 1024 # 最小压缩大小(字节)

      # 超时配置
      timeout:
        connect: 30 # 连接超时时间(秒)
        read: 60 # 读取超时时间(秒)
        write: 60 # 写入超时时间(秒)
        heartbeat: 30 # 心跳间隔(秒)

      # 连接限制配置
      connection-limit:
        enabled: true # 是否启用连接限制
        max-connections-per-user: 5 # 每用户最大连接数
        max-total-connections: 1000 # 总最大连接数
        max-connections-per-ip: 10 # 每IP最大连接数

      # 连接池配置
      pool:
        enabled: true # 是否启用连接池
        min-connections: 5 # 最小连接数
        max-connections: 50 # 最大连接数
        max-idle-time: 300 # 最大空闲时间(秒)
        max-connection-age: 3600 # 最大连接年龄(秒)
        connection-timeout: 30 # 连接超时时间(秒)
        validation-interval: 60 # 验证间隔(秒)
        enable-validation: true # 是否启用连接验证
        enable-recycling: true # 是否启用连接回收

      # 监控配置
      metrics:
        enabled: true # 是否启用指标收集
        export-format: "json" # 导出格式(json/prometheus)
        collection-interval: 60 # 收集间隔(秒)

      # 健康检查配置
      health:
        enabled: true # 是否启用健康检查
        check-interval: 30 # 检查间隔(秒)
        max-error-rate: 0.1 # 最大错误率(10%)
        max-response-time: 5000 # 最大响应时间(毫秒)
        min-success-rate: 0.9 # 最小成功率(90%)

    # Audio Starter - 音频播放功能
    audio:
      enabled: true # 是否启用音频功能

      # 音频播放配置
      player:
        enabled: true # 是否启用音频播放器
        volume: 1.0 # 播放音量(0.0-1.0)
        timeout_seconds: 30 # 播放超时时间(秒)
        supported_formats: [".mp3", ".wav", ".m4a", ".ogg"] # 支持的音频格式

      # TTS配置
      tts:
        enabled: true # 是否启用TTS功能
        engine: "pyttsx3" # TTS引擎
        language: "zh-CN" # 语言设置
        rate: 150 # 语速(50-300)
        volume: 1.0 # TTS音量(0.0-1.0)
