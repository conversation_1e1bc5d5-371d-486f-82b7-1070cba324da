#!/usr/bin/env python
"""
* @author: cz
* @description: 简化的异步配置属性

提供简洁的异步配置，只保留核心必要的配置项。
"""

from dataclasses import dataclass, field
from typing import Optional


@dataclass
class ThreadPoolConfig:
    """线程池配置"""
    
    # 基础线程池配置
    core_size: int = 2
    max_size: int = 4
    queue_capacity: int = 100
    keep_alive: int = 60
    thread_name_prefix: str = "miniboot-async-"
    allow_core_thread_timeout: bool = False
    
    # 拒绝策略
    rejection_policy: str = "abort"  # abort, caller_runs, discard, discard_oldest
    
    def validate(self) -> None:
        """验证线程池配置"""
        if self.core_size <= 0:
            raise ValueError("core_size must be positive")
        
        if self.max_size <= 0:
            raise ValueError("max_size must be positive")
        
        if self.core_size > self.max_size:
            raise ValueError("core_size cannot be greater than max_size")
        
        if self.queue_capacity < 0:
            raise ValueError("queue_capacity cannot be negative")
        
        if self.keep_alive < 0:
            raise ValueError("keep_alive cannot be negative")
        
        valid_policies = ["abort", "caller_runs", "discard", "discard_oldest"]
        if self.rejection_policy not in valid_policies:
            raise ValueError(f"Invalid rejection_policy: {self.rejection_policy}. Must be one of {valid_policies}")


@dataclass
class AsyncProperties:
    """简化的异步配置属性"""
    
    # 基础开关
    enabled: bool = True
    
    # 默认执行器配置
    executor: ThreadPoolConfig = field(default_factory=ThreadPoolConfig)
    
    # 可选的全局超时
    default_timeout: Optional[float] = None
    
    def validate(self) -> None:
        """验证配置参数"""
        if self.default_timeout is not None and self.default_timeout <= 0:
            raise ValueError("default_timeout must be positive")
        
        # 验证执行器配置
        self.executor.validate()
    
    def get_executor_config(self) -> ThreadPoolConfig:
        """获取执行器配置"""
        return self.executor
    
    def to_dict(self) -> dict:
        """转换为字典"""
        from dataclasses import asdict
        return asdict(self)