#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架异常处理器基础类

提供统一的异常处理接口和基础实现。
"""

from abc import ABC, abstractmethod
from typing import Any, Optional

from loguru import logger

from miniboot.utils.singleton import SingletonMeta

from ..base import MiniBootError


class ExceptionHandler(ABC):
    """异常处理器基类"""

    @abstractmethod
    def can_handle(self, exception: Exception) -> bool:
        """判断是否可以处理指定的异常

        Args:
            exception: 异常实例

        Returns:
            是否可以处理
        """
        pass

    @abstractmethod
    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理异常

        Args:
            exception: 异常实例
            context: 处理上下文

        Returns:
            处理结果
        """
        pass

    def get_priority(self) -> int:
        """获取处理器优先级

        Returns:
            优先级数值，数值越小优先级越高
        """
        return 100


class BaseExceptionHandler(ExceptionHandler):
    """基础异常处理器"""

    def __init__(self, exception_types: list[type[Exception]]):
        """初始化基础异常处理器

        Args:
            exception_types: 可处理的异常类型列表
        """
        self.exception_types = exception_types

    def can_handle(self, exception: Exception) -> bool:
        """检查是否可以处理指定的异常"""
        return any(isinstance(exception, exc_type) for exc_type in self.exception_types)

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """基础异常处理逻辑"""
        context = context or {}

        # 记录异常日志
        self._log_exception(exception, context)

        # 构建错误响应
        error_response = self._build_error_response(exception, context)

        return error_response

    def _log_exception(self, exception: Exception, context: dict[str, Any]):
        """记录异常日志"""
        if isinstance(exception, MiniBootError):
            logger.error(f"Exception handled: {exception.get_debug_message()}")
        else:
            logger.error(f"Exception handled: {type(exception).__name__}: {exception}")

        if context:
            logger.debug(f"Exception context: {context}")

    def _build_error_response(self, exception: Exception, context: dict[str, Any]) -> dict[str, Any]:
        """构建错误响应"""
        if isinstance(exception, MiniBootError):
            return exception.to_dict()
        else:
            return {
                "error_code": "UNKNOWN",
                "message": str(exception),
                "type": type(exception).__name__,
                "details": {},
                "context": context,
                "retryable": False,
                "max_attempts": 1,
            }


class RetryableExceptionHandler(BaseExceptionHandler):
    """可重试异常处理器"""

    def handle(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理可重试异常"""
        context = context or {}

        # 检查是否为可重试异常
        if isinstance(exception, MiniBootError) and exception.retryable:
            logger.warning(f"Retryable exception occurred: {exception.get_user_message()}")

            # 添加重试信息到上下文
            context.update({
                "retryable": True,
                "max_attempts": exception.max_attempts,
                "base_delay": exception.base_delay,
                "strategy": exception.strategy,
            })

        return super().handle(exception, context)


class GlobalExceptionCoordinator(metaclass=SingletonMeta):
    """全局异常协调器 - 单例模式"""

    def __init__(self):
        self.handlers: list[ExceptionHandler] = []
        self._default_handler = BaseExceptionHandler([Exception])

    def register_handler(self, handler: ExceptionHandler):
        """注册异常处理器

        Args:
            handler: 异常处理器实例
        """
        self.handlers.append(handler)
        # 按优先级排序
        self.handlers.sort(key=lambda h: h.get_priority())
        logger.debug(f"Registered exception handler: {type(handler).__name__}")

    def unregister_handler(self, handler: ExceptionHandler):
        """注销异常处理器

        Args:
            handler: 异常处理器实例
        """
        if handler in self.handlers:
            self.handlers.remove(handler)
            logger.debug(f"Unregistered exception handler: {type(handler).__name__}")

    def handle_exception(self, exception: Exception, context: Optional[dict[str, Any]] = None) -> Any:
        """处理异常

        Args:
            exception: 异常实例
            context: 处理上下文

        Returns:
            处理结果
        """
        # 延迟初始化异常处理器
        self._ensure_handlers_initialized()

        # 查找合适的处理器
        for handler in self.handlers:
            if handler.can_handle(exception):
                logger.debug(f"Using handler {type(handler).__name__} for exception {type(exception).__name__}")
                return handler.handle(exception, context)

        # 使用默认处理器
        logger.debug(f"Using default handler for exception {type(exception).__name__}")
        return self._default_handler.handle(exception, context)

    def _ensure_handlers_initialized(self):
        """确保处理器已初始化（延迟初始化）"""
        if not self.handlers:  # 如果没有注册任何处理器，则进行初始化
            try:
                # 避免循环导入
                from .. import ensure_handlers_initialized
                ensure_handlers_initialized()
            except ImportError:
                # 如果导入失败，静默处理
                pass

    def get_handlers(self) -> list[ExceptionHandler]:
        """获取所有注册的处理器

        Returns:
            处理器列表
        """
        return self.handlers.copy()

    def clear_handlers(self):
        """清除所有处理器"""
        self.handlers.clear()
        logger.debug("Cleared all exception handlers")
