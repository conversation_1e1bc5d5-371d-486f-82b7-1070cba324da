#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: AsyncProperties 和 ThreadPoolConfig 单元测试

测试异步配置属性的创建、验证和使用.
"""

import unittest

from miniboot.asyncs.properties import AsyncProperties, ThreadPoolConfig


class TestThreadPoolConfig(unittest.TestCase):
    """ThreadPoolConfig 测试类"""

    def test_default_config(self):
        """测试默认配置"""
        config = ThreadPoolConfig()

        self.assertEqual(config.core_size, 2)
        self.assertEqual(config.max_size, 4)
        self.assertEqual(config.queue_capacity, 100)
        self.assertEqual(config.keep_alive, 60)
        self.assertEqual(config.thread_name_prefix, "miniboot-async-")
        self.assertFalse(config.allow_core_thread_timeout)
        self.assertEqual(config.rejection_policy, "abort")

    def test_custom_config(self):
        """测试自定义配置"""
        config = ThreadPoolConfig(
            core_size=8,
            max_size=16,
            queue_capacity=200,
            keep_alive=120,
            thread_name_prefix="custom-",
            allow_core_thread_timeout=True,
            rejection_policy="caller_runs",
        )

        assert config.core_size == 8
        assert config.max_size == 16
        assert config.queue_capacity == 200
        assert config.keep_alive == 120
        assert config.thread_name_prefix == "custom-"
        assert config.allow_core_thread_timeout is True
        assert config.rejection_policy == "caller_runs"

    def test_validate_success(self):
        """测试配置验证成功"""
        config = ThreadPoolConfig(core_size=4, max_size=8, queue_capacity=100, keep_alive=60, rejection_policy="caller_runs")

        # 应该不抛出异常
        config.validate()

    def test_validate_core_size_negative(self):
        """测试核心线程数为负数"""
        config = ThreadPoolConfig(core_size=-1)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("core_size must be positive", str(cm.exception))

    def test_validate_core_size_zero(self):
        """测试核心线程数为零"""
        config = ThreadPoolConfig(core_size=0)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("core_size must be positive", str(cm.exception))

    def test_validate_max_size_negative(self):
        """测试最大线程数为负数"""
        config = ThreadPoolConfig(max_size=-1)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("max_size must be positive", str(cm.exception))

    def test_validate_max_size_zero(self):
        """测试最大线程数为零"""
        config = ThreadPoolConfig(max_size=0)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("max_size must be positive", str(cm.exception))

    def test_validate_core_size_greater_than_max_size(self):
        """测试核心线程数大于最大线程数"""
        config = ThreadPoolConfig(core_size=10, max_size=5)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("core_size cannot be greater than max_size", str(cm.exception))

    def test_validate_queue_capacity_negative(self):
        """测试队列容量为负数"""
        config = ThreadPoolConfig(queue_capacity=-1)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("queue_capacity cannot be negative", str(cm.exception))

    def test_validate_keep_alive_negative(self):
        """测试保活时间为负数"""
        config = ThreadPoolConfig(keep_alive=-1)

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("keep_alive cannot be negative", str(cm.exception))

    def test_validate_invalid_rejection_policy(self):
        """测试无效的拒绝策略"""
        config = ThreadPoolConfig(rejection_policy="invalid_policy")

        with self.assertRaises(ValueError) as cm:
            config.validate()
        self.assertIn("Invalid rejection_policy", str(cm.exception))

    def test_validate_all_rejection_policies(self):
        """测试所有有效的拒绝策略"""
        valid_policies = ["abort", "caller_runs", "discard", "discard_oldest"]

        for policy in valid_policies:
            config = ThreadPoolConfig(rejection_policy=policy)
            config.validate()  # 应该不抛出异常

    def test_edge_cases(self):
        """测试边界情况"""
        # 核心线程数等于最大线程数
        config = ThreadPoolConfig(core_size=4, max_size=4)
        config.validate()

        # 队列容量为零
        config = ThreadPoolConfig(queue_capacity=0)
        config.validate()

        # 保活时间为零
        config = ThreadPoolConfig(keep_alive=0)
        config.validate()


class TestAsyncProperties(unittest.TestCase):
    """AsyncProperties 测试类"""

    def test_default_properties(self):
        """测试默认属性"""
        props = AsyncProperties()

        assert props.enabled is True
        assert props.default_timeout is None
        assert isinstance(props.executor, ThreadPoolConfig)
        assert props.executor.core_size == 2
        assert props.executor.max_size == 4

    def test_custom_properties(self):
        """测试自定义属性"""
        custom_executor = ThreadPoolConfig(core_size=8, max_size=16, rejection_policy="caller_runs")

        props = AsyncProperties(enabled=False, executor=custom_executor, default_timeout=60.0)

        assert props.enabled is False
        assert props.default_timeout == 60.0
        assert props.executor.core_size == 8
        assert props.executor.max_size == 16
        assert props.executor.rejection_policy == "caller_runs"

    def test_validate_success(self):
        """测试验证成功"""
        props = AsyncProperties(enabled=True, default_timeout=30.0)

        # 应该不抛出异常
        props.validate()

    def test_validate_negative_timeout(self):
        """测试负数超时时间"""
        props = AsyncProperties(default_timeout=-1.0)

        with self.assertRaises(ValueError) as cm:
            props.validate()
        self.assertIn("default_timeout must be positive", str(cm.exception))

    def test_validate_zero_timeout(self):
        """测试零超时时间"""
        props = AsyncProperties(default_timeout=0.0)

        with self.assertRaises(ValueError) as cm:
            props.validate()
        self.assertIn("default_timeout must be positive", str(cm.exception))

    def test_validate_none_timeout(self):
        """测试 None 超时时间"""
        props = AsyncProperties(default_timeout=None)

        # 应该不抛出异常
        props.validate()

    def test_validate_executor_config(self):
        """测试执行器配置验证"""
        # 无效的执行器配置
        invalid_executor = ThreadPoolConfig(core_size=-1)
        props = AsyncProperties(executor=invalid_executor)

        with self.assertRaises(ValueError) as cm:
            props.validate()
        self.assertIn("core_size must be positive", str(cm.exception))

    def test_get_executor_config(self):
        """测试获取执行器配置"""
        custom_executor = ThreadPoolConfig(core_size=8, max_size=16)
        props = AsyncProperties(executor=custom_executor)

        executor_config = props.get_executor_config()

        assert executor_config is custom_executor
        assert executor_config.core_size == 8
        assert executor_config.max_size == 16

    def test_to_dict(self):
        """测试转换为字典"""
        props = AsyncProperties(enabled=True, default_timeout=30.0)

        result = props.to_dict()

        assert isinstance(result, dict)
        assert result["enabled"] is True
        assert result["default_timeout"] == 30.0
        assert "executor" in result
        assert isinstance(result["executor"], dict)
        assert result["executor"]["core_size"] == 2
        assert result["executor"]["max_size"] == 4

    def test_to_dict_with_custom_executor(self):
        """测试自定义执行器的字典转换"""
        custom_executor = ThreadPoolConfig(core_size=8, max_size=16, thread_name_prefix="test-")
        props = AsyncProperties(executor=custom_executor)

        result = props.to_dict()

        assert result["executor"]["core_size"] == 8
        assert result["executor"]["max_size"] == 16
        assert result["executor"]["thread_name_prefix"] == "test-"

    def test_complex_scenario(self):
        """测试复杂场景"""
        # 创建复杂的配置
        executor_config = ThreadPoolConfig(
            core_size=10,
            max_size=20,
            queue_capacity=500,
            keep_alive=300,
            thread_name_prefix="complex-test-",
            allow_core_thread_timeout=True,
            rejection_policy="discard_oldest",
        )

        props = AsyncProperties(enabled=True, executor=executor_config, default_timeout=120.0)

        # 验证配置
        props.validate()

        # 转换为字典
        config_dict = props.to_dict()

        # 验证结果
        assert config_dict["enabled"] is True
        assert config_dict["default_timeout"] == 120.0
        assert config_dict["executor"]["core_size"] == 10
        assert config_dict["executor"]["max_size"] == 20
        assert config_dict["executor"]["queue_capacity"] == 500
        assert config_dict["executor"]["keep_alive"] == 300
        assert config_dict["executor"]["thread_name_prefix"] == "complex-test-"
        assert config_dict["executor"]["allow_core_thread_timeout"] is True
        assert config_dict["executor"]["rejection_policy"] == "discard_oldest"


class TestIntegration(unittest.TestCase):
    """集成测试"""

    def test_properties_with_all_rejection_policies(self):
        """测试所有拒绝策略的属性配置"""
        policies = ["abort", "caller_runs", "discard", "discard_oldest"]

        for policy in policies:
            executor = ThreadPoolConfig(rejection_policy=policy)
            props = AsyncProperties(executor=executor)

            # 验证不应该抛出异常
            props.validate()

            # 验证策略设置正确
            assert props.executor.rejection_policy == policy

    def test_properties_serialization_roundtrip(self):
        """测试属性序列化往返"""
        original_props = AsyncProperties(
            enabled=True,
            default_timeout=45.0,
            executor=ThreadPoolConfig(core_size=6, max_size=12, queue_capacity=150, rejection_policy="caller_runs"),
        )

        # 转换为字典
        props_dict = original_props.to_dict()

        # 从字典重建（手动重建，因为没有 from_dict 方法）
        rebuilt_executor = ThreadPoolConfig(
            core_size=props_dict["executor"]["core_size"],
            max_size=props_dict["executor"]["max_size"],
            queue_capacity=props_dict["executor"]["queue_capacity"],
            keep_alive=props_dict["executor"]["keep_alive"],
            thread_name_prefix=props_dict["executor"]["thread_name_prefix"],
            allow_core_thread_timeout=props_dict["executor"]["allow_core_thread_timeout"],
            rejection_policy=props_dict["executor"]["rejection_policy"],
        )

        rebuilt_props = AsyncProperties(enabled=props_dict["enabled"], executor=rebuilt_executor, default_timeout=props_dict["default_timeout"])

        # 验证重建的属性与原始属性相同
        assert rebuilt_props.enabled == original_props.enabled
        assert rebuilt_props.default_timeout == original_props.default_timeout
        assert rebuilt_props.executor.core_size == original_props.executor.core_size
        assert rebuilt_props.executor.max_size == original_props.executor.max_size
        assert rebuilt_props.executor.queue_capacity == original_props.executor.queue_capacity
        assert rebuilt_props.executor.rejection_policy == original_props.executor.rejection_policy


if __name__ == '__main__':
    unittest.main()
