#!/usr/bin/env python
"""
* @author: cz
* @description: 异步处理模块

提供 Mini-Boot 框架的异步任务执行、线程池管理功能.

主要功能:
- 异步执行器 (Executor) - 异步任务执行和调度
- 线程池管理 (ThreadPoolTaskExecutor) - 线程池的创建和管理
- 异步配置 (AsyncProperties) - 异步功能配置
- 异步装饰器 (@Async 等异步相关注解)
"""

# 注解(引用现有注解模块)
try:
    from ..annotations.schedule import Async, EnableAsync
except ImportError:
    # 如果注解模块不存在，定义占位符
    Async = None
    EnableAsync = None

# 基础组件
from .base import Executor

# 任务执行器
from .executor import ThreadPoolTaskExecutor, ExecutorMetrics

# 简化的异步配置
from .properties import AsyncProperties, ThreadPoolConfig

# 异步工具函数（已迁移到 utils.asyncs）
try:
    from miniboot.utils.asyncs import (
        AsyncBatch, async_exception_handler, async_filter,
        async_map, async_timer, gather, retry_async, run_sync
    )
except ImportError:
    # 如果工具模块不存在，定义占位符
    AsyncBatch = None
    async_exception_handler = None
    async_filter = None
    async_map = None
    async_timer = None
    gather = None
    retry_async = None
    run_sync = None

__all__ = [
    # 注解
    "Async",
    "EnableAsync",
    
    # 基础组件
    "Executor",
    
    # 任务执行器
    "ThreadPoolTaskExecutor",
    "ExecutorMetrics",

    # 配置
    "AsyncProperties",
    "ThreadPoolConfig",

    # 异步工具函数
    "run_sync",
    "gather",
    "retry_async",
    "AsyncBatch",
    "async_timer",
    "async_map",
    "async_filter",
    "async_exception_handler",
]