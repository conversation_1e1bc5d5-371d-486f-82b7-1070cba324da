#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: ThreadPoolTaskExecutor 单元测试

测试线程池任务执行器的功能和性能.
"""

import asyncio
import time
import threading
import unittest
from concurrent.futures import Future

from miniboot.asyncs.executor import ThreadPoolTaskExecutor, ExecutorMetrics
from miniboot.asyncs.properties import ThreadPoolConfig


class TestThreadPoolTaskExecutor(unittest.TestCase):
    """ThreadPoolTaskExecutor 测试类"""

    def test_create_executor_with_default_config(self):
        """测试使用默认配置创建执行器"""
        config = ThreadPoolConfig()
        executor = ThreadPoolTaskExecutor(config, "test-executor")

        self.assertEqual(executor.name, "test-executor")
        self.assertFalse(executor.is_shutdown())
        self.assertIs(executor.config, config)

    def test_create_executor_with_custom_config(self):
        """测试使用自定义配置创建执行器"""
        config = ThreadPoolConfig(core_size=4, max_size=8, queue_capacity=200, thread_name_prefix="custom-")
        executor = ThreadPoolTaskExecutor(config, "custom-executor")

        self.assertEqual(executor.name, "custom-executor")
        self.assertEqual(executor.config.core_size, 4)
        self.assertEqual(executor.config.max_size, 8)
        self.assertEqual(executor.config.queue_capacity, 200)

    def test_submit_sync_function(self):
        """测试提交同步函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "sync-test")

        def sync_task(x, y):
            return x + y

        try:
            future = executor.submit(sync_task, 3, 5)
            result = future.result(timeout=5)

            self.assertEqual(result, 8)
            self.assertIsInstance(future, Future)
        finally:
            executor.shutdown()

    def test_submit_async_function(self):
        """测试提交异步函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "async-test")

        async def async_task(x, y):
            await asyncio.sleep(0.01)
            return x * y

        try:
            future = executor.submit(async_task, 4, 5)
            result = future.result(timeout=5)

            self.assertEqual(result, 20)
        finally:
            executor.shutdown()

    def test_submit_function_with_exception(self):
        """测试提交会抛出异常的函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "exception-test")

        def failing_task():
            raise ValueError("Test exception")

        try:
            future = executor.submit(failing_task)

            with self.assertRaises(ValueError) as cm:
                future.result(timeout=5)
            self.assertIn("Test exception", str(cm.exception))
        finally:
            executor.shutdown()

    def test_submit_async_function_with_exception(self):
        """测试提交会抛出异常的异步函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "async-exception-test")

        async def failing_async_task():
            await asyncio.sleep(0.01)
            raise RuntimeError("Async test exception")

        try:
            future = executor.submit(failing_async_task)

            with self.assertRaises(RuntimeError) as cm:
                future.result(timeout=5)
            self.assertIn("Async test exception", str(cm.exception))
        finally:
            executor.shutdown()

    def test_submit_with_args_and_kwargs(self):
        """测试提交带参数的函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "args-test")

        def complex_task(a, b, c=None, d=None):
            return f"a={a}, b={b}, c={c}, d={d}"

        try:
            future = executor.submit(complex_task, 1, 2, c=3, d=4)
            result = future.result(timeout=5)

            self.assertEqual(result, "a=1, b=2, c=3, d=4")
        finally:
            executor.shutdown()

    def test_multiple_tasks_execution(self):
        """测试多任务并发执行"""
        config = ThreadPoolConfig(core_size=3, max_size=6)
        executor = ThreadPoolTaskExecutor(config, "multi-test")

        def task(x):
            time.sleep(0.1)  # 模拟工作
            return x * 2

        try:
            # 提交多个任务
            futures = []
            for i in range(10):
                future = executor.submit(task, i)
                futures.append(future)

            # 等待所有任务完成
            results = [future.result(timeout=10) for future in futures]

            # 验证结果
            expected = [i * 2 for i in range(10)]
            self.assertEqual(results, expected)
        finally:
            executor.shutdown()

    def test_shutdown_behavior(self):
        """测试关闭行为"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "shutdown-test")

        # 初始状态
        self.assertFalse(executor.is_shutdown())

        # 关闭执行器
        executor.shutdown()

        # 验证状态
        self.assertTrue(executor.is_shutdown())

        # 关闭后不能提交新任务
        with self.assertRaises(RuntimeError) as cm:
            executor.submit(lambda: None)
        self.assertIn("has been shut down", str(cm.exception))

    def test_shutdown_with_wait(self):
        """测试带等待的关闭"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "shutdown-wait-test")

        def slow_task():
            time.sleep(0.2)
            return "completed"

        # 提交任务
        future = executor.submit(slow_task)

        # 关闭并等待
        start_time = time.time()
        executor.shutdown(wait=True)
        end_time = time.time()

        # 验证任务完成
        assert future.result() == "completed"
        assert executor.is_shutdown()
        # 验证等待了一段时间
        assert end_time - start_time >= 0.1

    def test_shutdown_without_wait(self):
        """测试不等待的关闭"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "shutdown-nowait-test")

        def slow_task():
            time.sleep(0.5)
            return "completed"

        # 提交任务
        executor.submit(slow_task)

        # 关闭但不等待
        start_time = time.time()
        executor.shutdown(wait=False)
        end_time = time.time()

        # 验证立即返回
        assert executor.is_shutdown()
        assert end_time - start_time < 0.1

    def test_get_metrics(self):
        """测试获取指标"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "metrics-test")

        try:
            # 初始指标
            metrics = executor.get_metrics()
            assert isinstance(metrics, dict)
            assert "total_tasks" in metrics
            assert "success_rate" in metrics
            assert "last_activity_time" in metrics

            # 执行一些任务
            future1 = executor.submit(lambda: "success")
            future1.result()

            future2 = executor.submit(lambda: 1 / 0)  # 会失败
            with self.assertRaises(ZeroDivisionError):
                future2.result()

            # 检查更新后的指标
            updated_metrics = executor.get_metrics()
            assert updated_metrics["total_tasks"] >= 2
        finally:
            executor.shutdown()

    def test_queue_capacity_limit(self):
        """测试队列容量限制"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=2, rejection_policy="abort")
        executor = ThreadPoolTaskExecutor(config, "queue-test")

        def slow_task():
            time.sleep(0.5)
            return "done"

        try:
            # 提交任务直到队列满
            futures = []

            # 第一个任务会立即开始执行
            future1 = executor.submit(slow_task)
            futures.append(future1)

            # 接下来的任务会进入队列
            future2 = executor.submit(slow_task)
            futures.append(future2)

            future3 = executor.submit(slow_task)
            futures.append(future3)

            # 队列满后，根据拒绝策略处理
            # 这里可能会抛出异常或使用其他策略

            # 等待任务完成
            import contextlib

            for future in futures:
                with contextlib.suppress(Exception):
                    future.result(timeout=10)

        finally:
            executor.shutdown()

    def test_rejection_policy_abort(self):
        """测试 abort 拒绝策略"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=1, rejection_policy="abort")
        executor = ThreadPoolTaskExecutor(config, "abort-test")

        def blocking_task():
            time.sleep(1.0)
            return "done"

        try:
            # 填满执行器和队列
            executor.submit(blocking_task)  # 正在执行
            executor.submit(blocking_task)  # 在队列中

            # 这个任务应该被拒绝（abort策略会抛出异常）
            with self.assertRaises(RuntimeError) as cm:
                executor.submit(blocking_task)
            self.assertIn("queue is full", str(cm.exception))

        finally:
            executor.shutdown(wait=False)

    def test_rejection_policy_caller_runs(self):
        """测试 caller_runs 拒绝策略"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=1, rejection_policy="caller_runs")
        executor = ThreadPoolTaskExecutor(config, "caller-runs-test")

        def blocking_task():
            time.sleep(0.1)
            return "done"

        try:
            # 填满执行器和队列
            future1 = executor.submit(blocking_task)  # 正在执行
            future2 = executor.submit(blocking_task)  # 在队列中

            # 这个任务应该在调用线程中执行
            result3 = executor.submit(blocking_task)

            # 验证所有任务都能完成
            assert future1.result() == "done"
            assert future2.result() == "done"
            # caller_runs 策略直接返回结果，不是 Future
            assert result3 == "done"

        finally:
            executor.shutdown(wait=False)

    def test_rejection_policy_discard(self):
        """测试 discard 拒绝策略"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=1, rejection_policy="discard")
        executor = ThreadPoolTaskExecutor(config, "discard-test")

        def blocking_task():
            time.sleep(0.1)
            return "done"

        try:
            # 填满执行器和队列
            future1 = executor.submit(blocking_task)  # 正在执行
            future2 = executor.submit(blocking_task)  # 在队列中

            # 这个任务应该被丢弃
            result3 = executor.submit(blocking_task)

            # 验证前两个任务完成，第三个被丢弃
            assert future1.result() == "done"
            assert future2.result() == "done"
            # discard 策略直接返回 None
            assert result3 is None

        finally:
            executor.shutdown(wait=False)

    def test_rejection_policy_discard_oldest(self):
        """测试 discard_oldest 拒绝策略"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=1, rejection_policy="discard_oldest")
        executor = ThreadPoolTaskExecutor(config, "discard-oldest-test")

        def blocking_task():
            time.sleep(0.1)
            return "done"

        try:
            # 填满执行器和队列
            future1 = executor.submit(blocking_task)  # 正在执行
            executor.submit(blocking_task)  # 在队列中

            # 这个任务应该替换队列中最老的任务
            future3 = executor.submit(blocking_task)

            # 验证第一个任务完成
            assert future1.result() == "done"
            # discard_oldest 策略的行为比较复杂，主要验证不抛出异常
            # future3 可能是 Future 对象或 None，取决于具体实现
            if hasattr(future3, "result"):
                # 如果是 Future，等待完成
                future3.result()
            # 主要目的是测试策略不会抛出异常

        finally:
            executor.shutdown(wait=False)

    def test_rejection_policy_invalid(self):
        """测试无效的拒绝策略"""
        config = ThreadPoolConfig(core_size=1, max_size=1, queue_capacity=1, rejection_policy="invalid_policy")
        executor = ThreadPoolTaskExecutor(config, "invalid-policy-test")

        def blocking_task():
            time.sleep(0.1)
            return "done"

        try:
            # 填满执行器和队列
            executor.submit(blocking_task)  # 正在执行
            executor.submit(blocking_task)  # 在队列中

            # 这个任务应该抛出 ValueError
            with self.assertRaises(ValueError) as cm:
                executor.submit(blocking_task)
            self.assertIn("Unknown rejection policy", str(cm.exception))

        finally:
            executor.shutdown(wait=False)

    def test_thread_safety(self):
        """测试线程安全性"""
        config = ThreadPoolConfig(core_size=4, max_size=8)
        executor = ThreadPoolTaskExecutor(config, "thread-safety-test")

        completed_tasks = []
        results_lock = threading.Lock()

        def simple_task():
            """简单任务，只返回固定值"""
            return "completed"

        def thread_task(thread_id):
            for i in range(10):
                try:
                    future = executor.submit(simple_task)
                    result = future.result(timeout=5)
                    with results_lock:
                        completed_tasks.append((thread_id, i, result))
                except Exception as e:
                    with results_lock:
                        completed_tasks.append((thread_id, i, f"error: {e}"))

        try:
            # 创建多个线程同时提交任务
            threads = []
            for thread_id in range(5):
                thread = threading.Thread(target=thread_task, args=(thread_id,))
                threads.append(thread)
                thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=10)

            # 验证结果
            assert len(completed_tasks) == 50  # 5个线程 * 10个任务

            # 验证所有任务都成功完成
            successful_tasks = [task for task in completed_tasks if task[2] == "completed"]
            assert len(successful_tasks) == 50, f"Expected 50 successful tasks, got {len(successful_tasks)}"

        finally:
            executor.shutdown()

    def test_executor_metrics_class(self):
        """测试 ExecutorMetrics 类"""
        metrics = ExecutorMetrics("test-executor")

        # 测试初始值
        assert metrics.total_tasks == 0
        assert metrics.completed_tasks == 0
        assert metrics.failed_tasks == 0

        # 测试 to_dict 方法
        metrics_dict = metrics.to_dict()
        assert metrics_dict["success_rate"] == 0.0

        # 测试更新
        metrics.total_tasks = 10
        metrics.completed_tasks = 8
        metrics.failed_tasks = 2

        # 成功率应该在 to_dict 中计算
        metrics_dict = metrics.to_dict()
        assert metrics_dict["success_rate"] == 80.0

    def test_async_function_with_asyncio_operations(self):
        """测试包含 asyncio 操作的异步函数"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "asyncio-test")

        async def complex_async_task():
            # 模拟异步 I/O 操作
            await asyncio.sleep(0.1)

            # 模拟一些计算
            result = sum(range(100))

            # 再次异步等待
            await asyncio.sleep(0.05)

            return result

        try:
            future = executor.submit(complex_async_task)
            result = future.result(timeout=5)

            assert result == sum(range(100))
        finally:
            executor.shutdown()

    def test_mixed_sync_and_async_tasks(self):
        """测试混合同步和异步任务"""
        config = ThreadPoolConfig(core_size=3, max_size=6)
        executor = ThreadPoolTaskExecutor(config, "mixed-test")

        def sync_task(x):
            time.sleep(0.05)
            return f"sync-{x}"

        async def async_task(x):
            await asyncio.sleep(0.05)
            return f"async-{x}"

        try:
            futures = []

            # 提交混合任务
            for i in range(10):
                future = executor.submit(sync_task, i) if i % 2 == 0 else executor.submit(async_task, i)
                futures.append(future)

            # 等待所有任务完成
            results = [future.result(timeout=10) for future in futures]

            # 验证结果
            for i, result in enumerate(results):
                if i % 2 == 0:
                    assert result == f"sync-{i}"
                else:
                    assert result == f"async-{i}"

        finally:
            executor.shutdown()

    def test_executor_name_property(self):
        """测试执行器名称属性"""
        config = ThreadPoolConfig()
        executor = ThreadPoolTaskExecutor(config, "test-name")

        assert executor.name == "test-name"

        # 名称应该是只读的（如果实现为属性）
        try:
            executor.name = "new-name"
            # 如果允许修改，验证修改成功
            assert executor.name == "new-name"
        except AttributeError:
            # 如果不允许修改，应该抛出 AttributeError
            assert executor.name == "test-name"

        executor.shutdown()

    def test_error_handling_in_async_wrapper(self):
        """测试异步包装器中的错误处理"""
        config = ThreadPoolConfig(core_size=2, max_size=4)
        executor = ThreadPoolTaskExecutor(config, "error-test")

        async def async_task_with_error():
            await asyncio.sleep(0.01)
            raise ValueError("Async error")

        try:
            future = executor.submit(async_task_with_error)

            with self.assertRaises(ValueError) as cm:
                future.result(timeout=5)
            self.assertIn("Async error", str(cm.exception))
        finally:
            executor.shutdown()

    def test_performance_with_many_tasks(self):
        """测试大量任务的性能"""
        config = ThreadPoolConfig(core_size=4, max_size=8, queue_capacity=1000)
        executor = ThreadPoolTaskExecutor(config, "performance-test")

        def simple_task(x):
            return x * x

        try:
            start_time = time.time()

            # 提交大量任务
            futures = []
            for i in range(100):
                future = executor.submit(simple_task, i)
                futures.append(future)

            # 等待所有任务完成
            results = [future.result(timeout=30) for future in futures]

            end_time = time.time()

            # 验证结果
            expected = [i * i for i in range(100)]
            assert results == expected

            # 验证性能（应该在合理时间内完成）
            execution_time = end_time - start_time
            assert execution_time < 10  # 应该在10秒内完成

        finally:
            executor.shutdown()


if __name__ == '__main__':
    unittest.main()
